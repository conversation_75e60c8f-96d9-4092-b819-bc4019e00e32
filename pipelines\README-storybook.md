# Storybook Pipeline Documentation

This directory contains Azure DevOps pipelines for building and deploying the Storybook application using Docker.

## Pipeline Files

### 1. `storybook.yaml` - Full Production Pipeline
A comprehensive pipeline that includes:
- Building and testing the Storybook Docker image
- Pushing to a container registry
- Deploying using Docker Compose
- Multiple stages with proper dependency management

**Use this for:** Production deployments with container registry integration

### 2. `storybook-simple.yaml` - Simple Build Pipeline
A streamlined pipeline that focuses on:
- Building the Storybook Docker image
- Running quality checks (TypeScript, linting)
- Testing the Docker image locally
- Testing Docker Compose deployment

**Use this for:** Development, testing, or when you don't need registry integration

### 3. `templates/docker-storybook.yaml` - Reusable Template
A template for Docker operations that can be reused across pipelines:
- Building Docker images
- Testing container functionality
- Displaying build information

## Setup Instructions

### For Simple Pipeline (Recommended to start)

1. **Create the pipeline in Azure DevOps:**
   ```bash
   # In Azure DevOps, create a new pipeline and point it to:
   pipelines/storybook-simple.yaml
   ```

2. **The pipeline will automatically trigger on:**
   - Pushes to `main` or `develop` branches
   - Changes to Storybook-related files (`src/**`, `.storybook/**`, `Dockerfile`, `docker-compose.yml`)
   - Pull requests to `main` branch

### For Full Production Pipeline

1. **Update variables in `storybook.yaml`:**
   ```yaml
   variables:
     dockerRegistryServiceConnection: 'your-docker-registry-connection'
     containerRegistry: 'your-registry.azurecr.io'
   ```

2. **Create service connections in Azure DevOps:**
   - Go to Project Settings > Service connections
   - Create a Docker Registry service connection
   - Update the `dockerRegistryServiceConnection` variable

3. **Create environment:**
   - Go to Pipelines > Environments
   - Create an environment named `storybook-environment`

## Local Testing

You can test the Docker setup locally:

```bash
# Build the Docker image
docker build -t rs-ui-react-components-storybook .

# Test the image
docker run -d --name storybook-test -p 8080:8080 rs-ui-react-components-storybook
curl http://localhost:8080
docker stop storybook-test && docker rm storybook-test

# Test with Docker Compose
docker-compose up -d
curl http://localhost:6006
docker-compose down
```

## Pipeline Features

### Quality Checks
- TypeScript type checking
- Biome linting
- Local Storybook build validation

### Docker Operations
- Multi-stage Docker build
- Container testing
- Image artifact creation
- Docker Compose validation

### Deployment Options
- Local testing
- Container registry push
- Docker Compose deployment
- Environment-based deployments

## Customization

### Changing Ports
Update the ports in:
- `docker-compose.yml` (external port)
- `Dockerfile` (internal port - currently 8080)
- Pipeline test scripts

### Adding Stages
You can extend the pipelines by adding stages for:
- Security scanning
- Performance testing
- Multi-environment deployments
- Integration testing

### Environment Variables
Add environment-specific variables in Azure DevOps:
- Go to Pipelines > Library
- Create variable groups for different environments

## Troubleshooting

### Common Issues

1. **Docker build fails:**
   - Check if all dependencies are in `package.json`
   - Verify `pnpm-lock.yaml` is up to date
   - Check `.dockerignore` isn't excluding necessary files

2. **Container doesn't start:**
   - Check Docker logs: `docker logs <container-name>`
   - Verify port mappings
   - Ensure Storybook builds successfully locally

3. **Pipeline fails on tests:**
   - Check if the container is accessible on the expected port
   - Verify curl commands in pipeline scripts
   - Check for port conflicts

### Debugging Commands

```bash
# Check running containers
docker ps

# View container logs
docker logs <container-name>

# Test container manually
docker run -it rs-ui-react-components-storybook sh

# Check image layers
docker history rs-ui-react-components-storybook
```

## Next Steps

1. Start with `storybook-simple.yaml` to validate the setup
2. Once working, consider upgrading to `storybook.yaml` for production
3. Add security scanning stages if needed
4. Integrate with your deployment infrastructure
5. Consider adding automated testing of Storybook components
