name: rs-ui-react-components Storybook Build

# Trigger on changes to storybook-related files
trigger:
  branches:
    include:
      - main
      - develop
  paths:
    include:
      - src/**
      - .storybook/**
      - Dockerfile
      - docker-compose.yml

# Also trigger on pull requests
pr:
  branches:
    include:
      - main
  paths:
    include:
      - src/**
      - .storybook/**
      - Dockerfile
      - docker-compose.yml

variables:
  imageRepository: 'rs-ui-react-components-storybook'
  dockerfilePath: '$(Build.SourcesDirectory)/Dockerfile'
  tag: '$(Build.BuildId)'
  nodeVersion: '22.x'
  pnpm_config_cache: $(Pipeline.Workspace)/.pnpm-store

pool:
  vmImage: 'ubuntu-latest'

stages:
  - stage: BuildAndTest
    displayName: Build and Test Storybook
    jobs:
      - job: BuildStorybook
        displayName: Build Storybook Docker Image
        timeoutInMinutes: 15
        
        steps:
          # Setup Node.js and dependencies for validation
          - template: templates/setup.yaml
            parameters:
              nodeVersion: $(nodeVersion)
              pnpm_config_cache: $(pnpm_config_cache)

          # Run quality checks
          - script: |
              pnpm run typecheck
            displayName: 'TypeScript type checking'

          - script: |
              pnpm run lint
            displayName: 'Biome linting'

          # Build Storybook locally for validation
          - script: |
              pnpm run build-storybook
            displayName: 'Build Storybook (validation)'

          # Use the Docker template
          - template: templates/docker-storybook.yaml
            parameters:
              imageRepository: $(imageRepository)
              dockerfilePath: $(dockerfilePath)
              tag: $(tag)
              testPort: '8080'

          # Save Docker image as artifact (optional)
          - script: |
              docker save $(imageRepository):$(tag) | gzip > $(Build.ArtifactStagingDirectory)/storybook-image-$(tag).tar.gz
            displayName: 'Save Docker image as artifact'
            condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Docker image artifact'
            condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
            inputs:
              PathtoPublish: '$(Build.ArtifactStagingDirectory)'
              ArtifactName: 'storybook-docker-image'
              publishLocation: 'Container'

          # Test with docker-compose
          - script: |
              # Update docker-compose.yml to use the built image
              sed -i "s|image: rs-ui-react-components-storybook|image: $(imageRepository):$(tag)|g" docker-compose.yml
              
              # Start services
              docker-compose up -d
              
              # Wait and test
              sleep 15
              if curl -f http://localhost:6006; then
                echo "Docker Compose deployment successful!"
              else
                echo "Docker Compose deployment failed"
                docker-compose logs
                exit 1
              fi
              
              # Cleanup
              docker-compose down
            displayName: 'Test Docker Compose deployment'
