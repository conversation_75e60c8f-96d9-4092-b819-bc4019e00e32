name: rs-ui-react-components Storybook Pipeline

# Trigger on changes to storybook-related files or main branch
trigger:
  branches:
    include:
      - main
      - develop
  paths:
    include:
      - src/**
      - .storybook/**
      - Dockerfile
      - docker-compose.yml
      - package.json
      - pnpm-lock.yaml

# Allow manual triggers
pr: none

variables:
  # Docker image configuration
  dockerRegistryServiceConnection: 'your-docker-registry-connection' # Update this
  imageRepository: 'rs-ui-react-components-storybook'
  containerRegistry: 'your-registry.azurecr.io' # Update this
  dockerfilePath: '$(Build.SourcesDirectory)/Dockerfile'
  tag: '$(Build.BuildId)'
  
  # Build configuration
  nodeVersion: '22.x'
  pnpm_config_cache: $(Pipeline.Workspace)/.pnpm-store

stages:
  - stage: Build
    displayName: Build Storybook
    jobs:
      - job: BuildStorybook
        displayName: Build Storybook Docker Image
        timeoutInMinutes: 15
        pool:
          vmImage: 'ubuntu-latest'
        
        steps:
          # Setup Node.js and dependencies for validation
          - template: templates/setup.yaml
            parameters:
              nodeVersion: $(nodeVersion)
              pnpm_config_cache: $(pnpm_config_cache)

          # Run linting and type checking before building Docker image
          - script: |
              pnpm run typecheck
            displayName: 'Run TypeScript type checking'

          - script: |
              pnpm run lint
            displayName: 'Run Biome linting'

          # Build and test Storybook locally first
          - script: |
              pnpm run build-storybook
            displayName: 'Build Storybook locally (validation)'

          # Build Docker image
          - task: Docker@2
            displayName: 'Build Storybook Docker image'
            inputs:
              command: 'build'
              repository: $(imageRepository)
              dockerfile: $(dockerfilePath)
              tags: |
                $(tag)
                latest
              arguments: '--no-cache'

          # Test the Docker image
          - script: |
              # Start container in background
              docker run -d --name storybook-test -p 8080:8080 $(imageRepository):$(tag)
              
              # Wait for container to start
              sleep 10
              
              # Test if Storybook is accessible
              curl -f http://localhost:8080 || exit 1
              
              # Stop and remove test container
              docker stop storybook-test
              docker rm storybook-test
            displayName: 'Test Docker image'

          # Save Docker image as artifact
          - script: |
              docker save $(imageRepository):$(tag) | gzip > $(Build.ArtifactStagingDirectory)/storybook-image.tar.gz
            displayName: 'Save Docker image as artifact'

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Docker image artifact'
            inputs:
              PathtoPublish: '$(Build.ArtifactStagingDirectory)'
              ArtifactName: 'storybook-docker-image'
              publishLocation: 'Container'

  # Optional: Push to Container Registry
  - stage: PushToRegistry
    displayName: Push to Container Registry
    dependsOn: Build
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - job: PushImage
        displayName: Push Docker Image
        pool:
          vmImage: 'ubuntu-latest'
        
        steps:
          # Download the Docker image artifact
          - task: DownloadBuildArtifacts@0
            displayName: 'Download Docker image artifact'
            inputs:
              buildType: 'current'
              downloadType: 'single'
              artifactName: 'storybook-docker-image'
              downloadPath: '$(System.ArtifactsDirectory)'

          # Load Docker image from artifact
          - script: |
              gunzip -c $(System.ArtifactsDirectory)/storybook-docker-image/storybook-image.tar.gz | docker load
            displayName: 'Load Docker image from artifact'

          # Tag image for registry
          - script: |
              docker tag $(imageRepository):$(tag) $(containerRegistry)/$(imageRepository):$(tag)
              docker tag $(imageRepository):$(tag) $(containerRegistry)/$(imageRepository):latest
            displayName: 'Tag image for registry'

          # Push to container registry
          - task: Docker@2
            displayName: 'Push image to container registry'
            inputs:
              command: 'push'
              repository: $(imageRepository)
              containerRegistry: $(dockerRegistryServiceConnection)
              tags: |
                $(tag)
                latest

  # Optional: Deploy using Docker Compose
  - stage: Deploy
    displayName: Deploy Storybook
    dependsOn: PushToRegistry
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: DeployStorybook
        displayName: Deploy Storybook
        pool:
          vmImage: 'ubuntu-latest'
        environment: 'storybook-environment' # Create this environment in Azure DevOps
        strategy:
          runOnce:
            deploy:
              steps:
                # Copy docker-compose.yml to deployment agent
                - script: |
                    echo "version: '3.8'
                    
                    services:
                      nextjs_app:
                        image: $(containerRegistry)/$(imageRepository):$(tag)
                        ports:
                          - \"6006:8080\"
                        restart: always" > docker-compose.yml
                  displayName: 'Create docker-compose.yml'

                # Deploy using docker-compose
                - script: |
                    docker-compose down || true
                    docker-compose pull
                    docker-compose up -d
                  displayName: 'Deploy with Docker Compose'

                # Verify deployment
                - script: |
                    sleep 15
                    curl -f http://localhost:6006 || exit 1
                  displayName: 'Verify deployment'
