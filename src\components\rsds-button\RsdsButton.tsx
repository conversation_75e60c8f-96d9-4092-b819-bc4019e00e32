import type { ButtonHTMLAttributes } from "react";
import "../../styles/public/tailwind.css";

export interface RsdsButtonProps
	extends ButtonHTMLAttributes<HTMLButtonElement> {
	/**
	 * Button contents
	 */
	children: React.ReactNode;
	/**
	 * Optional click handler
	 */
	onClick?: () => void;
	/**
	 * Button variant
	 */
	variant?: "main" | "default";

	form?: "rounded" | "square";
}

/**
 * Primary UI component for user interaction
 */
export const RsdsButton = ({
	children,
	variant = "default",
	form = "rounded",

	...props
}: RsdsButtonProps) => {
	const baseClasses = `inline-flex
	px-6 py-3 
	justify-center 
	items-start
	gap-4
	border-2 
	ring-offset-2
	ring-ct-focus
	disabled:bg-ct-main-button-bg--disabled
	disabled:text-ct-main-button-fg--disabled
	focus:ring 
	`;

	const variantClasses = {
		main: `bg-ct-main-button-bg
		text-ct-main-button-fg
		border-transparent
		
		hover:bg-ct-main-button-bg--hover
		hover:text-ct-main-button-fg
		
		active:bg-ct-main-button-bg--active 

		
		`,

		default: `border-ct-border 
		bg-ct-button-bg 
		text-ct-foreground

		hover:bg-ct-button-bg--hover

		active:bg-ct-button-bg--active
		`,
	};

	const formClasses = {
		rounded: "rounded-full",
		square: "rounded-sm",
	};

	return (
		<button
			type="button"
			className={`${baseClasses} ${variantClasses[variant]} ${formClasses[form]}`}
			{...props}
		>
			{children}
		</button>
	);
};

export default RsdsButton;
